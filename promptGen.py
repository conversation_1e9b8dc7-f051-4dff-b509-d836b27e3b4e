

import json

def read_json_file(file_path):


    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        return data
    except Exception as e:
        print(f"Error reading the JSON file: {e}")
        return None


def generate_prompt(sast_results, rag_results,context_info,source_code):

    prompt = f"""

任务: 你是一个漏洞检测和安全分析专家，负责评估和过滤潜在的漏洞风险。你的任务是根据以下内容分析代码：
--你的回复最后必须是如下的json！！！！：
   
      - 源代码中确认存在漏洞的CWE_ID:
        推理过程：详细描述确认的漏洞。
    
1. 源代码: 
"""
    prompt +=source_code
    prompt+="""
2. 逐步推理（COT）过程:
    - 请对提供的源代码进行分析，识别潜在的漏洞风险。

"""
    prompt+="""
3. 从静态分析工具（SAST）获得的漏洞警告:
   我们通过 SAST 工具进行了初步的静态代码分析，其中每个漏洞包含漏洞位置、漏洞类型和详细描述。
   结果1的数据如下：
"""
    prompt +=sast_results
    prompt += """
4. 通过 RAG（基于相似代码的检索和语义分析）方法获得
- - 请注意，RAG 方法可能会产生的结果只是单纯的与代码相关的CWE,而非对该代码的实际检测结果，请不用把它当作漏洞，而是单纯的参考。

"""

    # 将RAG的结果嵌入prompt
    for result in rag_results:
        prompt += f"""
   - CWE_ID: {result['CWE_ID']}
    距离: {result['distance']}
"""
    #文件名称: {result['file_name']}
     #代码片段: {result['code']}
    
  
    prompt += """
5. 代码上下文信息
"""  
    prompt +=context_info
 # 距离: {result['distance']}
    prompt += """
- - 注意：RAG 方法通过寻找与源代码相似的已知漏洞样本，帮助模型识别代码，他扮演的是注意力层的角色，而非真正的检测器。
- - 请注意，一定要详细考虑与RAG方法返回CWE相关的其他CWE，如CWE-416与CWE-415具有相似性，都应考虑。
    """
#     要求:
# - 结合结果1和结果2，标记出哪些是误报漏洞并进行过滤。
# - 请你结合上述结果，独立推断出未被检测出的新漏洞，并提供相关背景信息。

    prompt += """

-  请严格过滤出现的误报漏洞 对于较为罕见的情况（例如malloc分配失败导致指针为空）请不用考虑
   请独立自主的进行思考，不完全依赖RAG与SAST 得到你可以确认的CWE  以及相应推理过程
 
代码中可能含有多个cwe 请注意！
--你的回复最后必须是如下的json！！！！：
{
      - 源代码中确认存在漏洞的CWE_ID:
        推理过程：详细描述确认的漏洞。
}
"""

    return prompt

def generate_prompt_2(sast_results, rag_results,source_code):

    prompt = f"""
任务: 你是一个漏洞检测和安全分析专家，负责评估和过滤潜在的漏洞风险。你的任务是根据以下内容分析代码：
1. 源代码: 
"""
    prompt +=source_code
    prompt+="""
   严格推理很有信心的确认漏洞CWE_ID 不是CVE-ID!!!按照以下格式回复
   代码中可能含有多个cwe 请注意
--回复格式必须包含有如下json：
{
      - 源代码中确认存在漏洞的CWE_ID:
        推理过程：详细描述确认的漏洞。
}

"""

    return prompt

