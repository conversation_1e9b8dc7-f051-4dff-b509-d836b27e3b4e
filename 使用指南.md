# 智能代码漏洞检测系统使用指南

## 快速开始

### 环境要求
- Python 3.10+
- Neo4j 数据库
- PVS-Studio 静态分析工具
- Joern 代码分析器
- 足够的系统资源 (8GB+ RAM, 100GB+ 存储)

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd vulnerability-detection-system
```

2. **安装Python依赖**
```bash
pip install -r requirements.txt
```

3. **配置环境变量**
```bash
export NEO4J_URI=bolt://localhost:7687
export NEO4J_USER=neo4j
export NEO4J_PASSWORD=your_password
export CODEBERT_MODEL_PATH=/path/to/codebert-base
export PVS_STUDIO_PATH=/opt/pvs-studio
export JOERN_PATH=/opt/joern
```

4. **启动Neo4j数据库**
```bash
neo4j start
```

5. **准备FAISS索引**
```bash
# 如果没有预构建的索引，需要先构建
python build_faiss_index.py
```

## 基本使用

### 单文件分析
```python
from main import requestLLm

# 分析单个C文件
with open("example.c", "r") as f:
    code_content = f.read()

result = requestLLm(code_content, "example.c")
print(result)
```

### 批量文件分析
```python
from main import main

# 修改main.py中的目录路径
# c_files_dir = Path("/your/code/directory")
main()
```

### 命令行使用
```bash
# 分析指定目录下的所有C文件
python main.py --input-dir /path/to/code --output-dir /path/to/results

# 分析单个文件
python main.py --file /path/to/file.c --output results.json
```

## 配置说明

### 1. API配置 (main.py)
```python
# SiliconFlow API配置
url = "https://api.siliconflow.cn/v1/chat/completions"
headers = {
    "Authorization": "Bearer YOUR_API_KEY",  # 替换为您的API密钥
    "Content-Type": "application/json"
}

payload = {
    "model": "Qwen/QwQ-32B",
    "max_tokens": 13000,
    "temperature": 0.7,
    # 其他参数...
}
```

### 2. 静态分析配置 (SAST.py)
```python
# PVS-Studio输出目录
output_dir = "/root/preprocess/SAST"  # 修改为您的路径

# 分析命令配置
cmd = ['pvs-studio-analyzer', 'analyze', 
       '-f', 'compile_commands.json',
       '-o', 'pvs.log']
```

### 3. 向量检索配置 (utils.py)
```python
# CodeBERT模型路径
LOCAL_MODEL_DIR = "/root/codebert-base"  # 修改为您的模型路径

# FAISS索引路径
index_path = "/root/preprocess/large_embedding_index.faiss"  # 修改路径
```

### 4. Neo4j配置 (neo4j.py)
```python
# 数据库连接配置
neo4j_uri = "bolt://localhost:7687"
neo4j_user = "neo4j"
neo4j_password = "your_password"
```

## 输出格式

### 检测结果
系统会生成以下输出文件：

1. **原始响应** (`*_raw_response.json`)
```json
{
  "choices": [{
    "message": {
      "content": "LLM的完整响应内容"
    }
  }]
}
```

2. **解决方案** (`*_solution2.md`)
- LLM分析的Markdown格式报告

3. **综合结果** (`combined_results.json`)
```json
[{
  "input_file": "文件名",
  "prompt": "发送给LLM的提示",
  "response": "LLM的响应内容"
}]
```

### 漏洞检测结果格式
```json
{
  "源代码中确认存在漏洞的CWE_ID": "CWE-XXX",
  "推理过程": "详细的漏洞分析过程和确认依据"
}
```

## 高级功能

### 1. 自定义分析规则
修改 `promptGen.py` 中的提示模板：
```python
def generate_prompt(sast_results, rag_results, context_info, source_code):
    prompt = f"""
    自定义任务描述...
    
    分析要求：
    1. 重点关注特定CWE类型
    2. 考虑项目特定的安全要求
    3. 应用自定义的误报过滤规则
    """
    return prompt
```

### 2. 扩展数据源
在 `DB.py` 中添加新的漏洞数据源：
```python
class CustomVulnerabilitySource:
    def fetch_vulnerabilities(self):
        # 实现自定义数据获取逻辑
        pass
```

### 3. 集成新的静态分析工具
在 `SAST.py` 中添加新工具支持：
```python
def run_custom_analyzer(code_path):
    # 集成其他静态分析工具
    pass
```

## 性能优化

### 1. 并行处理
```python
import multiprocessing
from concurrent.futures import ProcessPoolExecutor

def analyze_files_parallel(file_list):
    with ProcessPoolExecutor(max_workers=4) as executor:
        results = executor.map(requestLLm, file_list)
    return list(results)
```

### 2. 缓存机制
```python
import functools

@functools.lru_cache(maxsize=1000)
def cached_embedding(code_hash):
    # 缓存代码嵌入结果
    pass
```

### 3. 批量处理优化
```python
# 批量处理多个文件
def batch_analyze(file_paths, batch_size=10):
    for i in range(0, len(file_paths), batch_size):
        batch = file_paths[i:i+batch_size]
        # 处理批次
        yield process_batch(batch)
```

## 故障排除

### 常见问题

1. **PVS-Studio许可证错误**
```bash
# 检查许可证状态
pvs-studio-analyzer --version
# 重新激活许可证
pvs-studio-analyzer activate <license-key>
```

2. **Neo4j连接失败**
```bash
# 检查服务状态
neo4j status
# 重启服务
neo4j restart
```

3. **FAISS索引损坏**
```python
# 重建索引
python build_faiss_index.py --rebuild
```

4. **内存不足**
```python
# 减少批处理大小
batch_size = 5  # 默认10

# 启用内存映射
import mmap
# 使用内存映射读取大文件
```

### 日志分析
```python
import logging

# 启用详细日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('debug.log'),
        logging.StreamHandler()
    ]
)
```

## 最佳实践

### 1. 代码准备
- 确保代码可编译
- 移除敏感信息
- 保持代码结构完整

### 2. 分析策略
- 先运行小样本测试
- 逐步增加分析范围
- 定期更新漏洞数据库

### 3. 结果验证
- 人工验证关键发现
- 建立误报反馈机制
- 持续改进检测规则

### 4. 性能监控
- 监控系统资源使用
- 记录分析时间统计
- 优化瓶颈环节

## 扩展开发

### 添加新的检测模型
```python
class CustomDetectionModel:
    def __init__(self, model_path):
        self.model = load_model(model_path)
    
    def detect_vulnerabilities(self, code):
        # 实现自定义检测逻辑
        return detection_results
```

### 集成IDE插件
```python
# VS Code插件接口
def vscode_analyze_current_file():
    current_file = get_active_file()
    result = requestLLm(current_file.content, current_file.path)
    display_results_in_editor(result)
```

### CI/CD集成
```yaml
# GitHub Actions示例
name: Vulnerability Detection
on: [push, pull_request]
jobs:
  security-scan:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Run Vulnerability Detection
        run: python main.py --input-dir ./src --output-dir ./security-reports
```

## 支持与反馈

如果您在使用过程中遇到问题或有改进建议，请：

1. 查看详细的技术文档
2. 检查GitHub Issues
3. 提交Bug报告或功能请求
4. 参与社区讨论

---

**注意**: 本系统仍在持续开发中，建议在生产环境使用前进行充分测试。
