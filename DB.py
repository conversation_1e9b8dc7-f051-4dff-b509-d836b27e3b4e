
import requests
import json
import logging
import time
import re
import os
from datetime import datetime
from typing import Dict, List, Optional
from urllib.parse import urljoin
import hashlib
from dataclasses import dataclass, asdict
from bs4 import BeautifulSoup
import zipfile

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('vulnerability_crawler.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class VulnerabilityRecord:
    """漏洞数据"""
    file_name: str
    CWE_ID: str
    If_Vul: bool
    Code: str
    Embedding: List[float]  # 暂时为空列表，预留字段

class GitHubVulnerabilityCrawler:

    
    def __init__(self, github_token: str = None):
        self.github_token = github_token
        self.session = requests.Session()
        if github_token:
            self.session.headers.update({
                'Authorization': f'token {github_token}',
                'Accept': 'application/vnd.github.v3+json'
            })
        
        # 漏洞相关关键词和对应的CWE映射
        self.vulnerability_patterns = {
            'buffer overflow': 'CWE-119',
            'buffer overrun': 'CWE-119',
            'sql injection': 'CWE-89',
            'xss': 'CWE-79',
            'cross-site scripting': 'CWE-79',
            'csrf': 'CWE-352',
            'remote code execution': 'CWE-94',
            'privilege escalation': 'CWE-269',
            'format string': 'CWE-134',
            'integer overflow': 'CWE-190',
            'use after free': 'CWE-416',
            'null pointer dereference': 'CWE-476',
            'race condition': 'CWE-362',
            'memory leak': 'CWE-401',
            'double free': 'CWE-415',
            'path traversal': 'CWE-22',
            'command injection': 'CWE-78',
            'insecure deserialization': 'CWE-502',
            'server-side request forgery': 'CWE-918',
            'ssrf': 'CWE-918',
            'xml external entity': 'CWE-611',
            'xxe': 'CWE-611',
            'broken authentication': 'CWE-287',
            'insecure direct object reference': 'CWE-639',
            'idor': 'CWE-639',
            'security misconfiguration': 'CWE-16',
            'improper access control': 'CWE-284',
            'unrestricted upload of file with dangerous type': 'CWE-434',
        }
        
        self.language_extensions = {
            'C': ['.c', '.h']
        }
    
    def search_vulnerability_commits(self, languages: List[str] = ['C', 'C++', 'Java', 'Python'], 
                                  max_results: int = 100) -> List[VulnerabilityRecord]:
        """
        搜索GitHub上的漏洞修复提交
        """
        records = []
        processed_commits = set()  # 避免重复处理
        
        logger.info(f"开始搜索GitHub漏洞提交，目标语言: {languages}")
        
        for language in languages:
            logger.info(f"正在搜索 {language} 语言的漏洞...")
            
            for pattern, cwe_id in self.vulnerability_patterns.items():
                try:
                    # 构建搜索查询
                    query = f'"{pattern}" fix security vulnerability language:{language} type:commit'
                    commits = self._search_commits(query, max_results // (len(languages) * len(self.vulnerability_patterns)))
                    
                    for commit in commits:
                        commit_key = f"{commit.get('repository', {}).get('full_name', '')}_{commit.get('sha', '')}"
                        if commit_key in processed_commits:
                            continue
                        
                        processed_commits.add(commit_key)
                        record = self._process_commit(commit, cwe_id, language)
                        if record:
                            records.append(record)
                            logger.info(f"找到漏洞记录: {record.file_name} ({record.CWE_ID})")
                    
                    # 避免API限制
                    time.sleep(1)
                    
                except Exception as e:
                    logger.error(f"搜索模式 '{pattern}' 在 {language} 中失败: {e}")
                    continue
        
        logger.info(f"从GitHub获取到 {len(records)} 条漏洞记录")
        return records
    
    def _search_commits(self, query: str, max_results: int) -> List[Dict]:
        """搜索提交"""
        commits = []
        per_page = min(30, max_results)
        max_pages = max(1, (max_results + per_page - 1) // per_page)
        
        for page in range(1, max_pages + 1):
            try:
                url = 'https://api.github.com/search/commits'
                params = {
                    'q': query,
                    'sort': 'committer-date',
                    'order': 'desc',
                    'per_page': per_page,
                    'page': page
                }
                
                response = self.session.get(url, params=params)
                
                if response.status_code == 403:
                    time.sleep(60)
                    continue
                    
                response.raise_for_status()
                data = response.json()
                
                items = data.get('items', [])
                commits.extend(items)
                
                if len(items) < per_page:
                    break
                    
            except Exception as e:
                logger.error(f"搜索提交失败 : {e}")
                break
        
        return commits[:max_results]
    
    def _process_commit(self, commit_data: Dict, cwe_id: str, language: str) -> Optional[VulnerabilityRecord]:
        """处理单个提交，提取漏洞信息"""
        try:
            repo_name = commit_data.get('repository', {}).get('full_name', '')
            commit_sha = commit_data.get('sha', '')
            commit_message = commit_data.get('commit', {}).get('message', '')
            
            # 获取提交详细信息
            commit_details = self._get_commit_details(repo_name, commit_sha)
            if not commit_details:
                return None
            
            # 提取代码变更
            files = commit_details.get('files', [])
            for file_info in files:
                filename = file_info.get('filename', '')
                
                # 只处理目标语言的代码文件
                if not self._is_target_language_file(filename, language):
                    continue
                
                patch = file_info.get('patch', '')
                vulnerable_code = self._extract_vulnerable_code(patch)
                
                # 验证代码质量
                if self._is_valid_vulnerable_code(vulnerable_code, commit_message):
                    # 尝试从提交信息中提取更精确的CWE ID
                    extracted_cwe = self._extract_cwe_from_message(commit_message)
                    final_cwe = extracted_cwe if extracted_cwe else cwe_id
                    
                    return VulnerabilityRecord(
                        file_name=filename,
                        CWE_ID=final_cwe,
                        If_Vul=True,
                        Code=vulnerable_code,
                        Embedding=[]  # 空列表，预留字段
                    )
            
            return None
            
        except Exception as e:
            logger.error(f"处理提交失败: {e}")
            return None
    
    def _get_commit_details(self, repo_name: str, commit_sha: str) -> Optional[Dict]:
        """获取提交详细信息"""
        try:
            url = f'https://api.github.com/repos/{repo_name}/commits/{commit_sha}'
            response = self.session.get(url)
            
            if response.status_code == 403:
             
                return None
                
            response.raise_for_status()
            return response.json()
        except Exception as e:
            
            return None
    
    def _is_target_language_file(self, filename: str, target_language: str) -> bool:

        if target_language not in self.language_extensions:
            return False
        
        extensions = self.language_extensions[target_language]
        return any(filename.lower().endswith(ext) for ext in extensions)
    
    def _extract_vulnerable_code(self, patch: str) -> str:
        vulnerable_lines = []
        
        for line in patch.split('\n'):
            # 提取被删除的行（漏洞代码）
            if line.startswith('-') and not line.startswith('---'):
                code_line = line[1:].strip()
                if code_line and not code_line.startswith('//') and not code_line.startswith('/*'):
                    vulnerable_lines.append(code_line)
        
        return '\n'.join(vulnerable_lines).strip()
    
    def _is_valid_vulnerable_code(self, code: str, commit_message: str) -> bool:
        """验证漏洞代码的有效性"""
        if not code or len(code.strip()) < 10:
            return False
        
        # 检查是否包含实际的代码结构
        code_indicators = ['(', ')', '{', '}', ';', '=', 'if', 'for', 'while', 'function', 'def', 'class']
        if not any(indicator in code.lower() for indicator in code_indicators):
            return False
        return True
    
    def _extract_cwe_from_message(self, commit_message: str) -> Optional[str]:
        """从提交信息中提取CWE ID"""
        # 查找CWE-数字格式
        cwe_pattern = r'CWE-(\d+)'
        match = re.search(cwe_pattern, commit_message, re.IGNORECASE)
        if match:
            return f"CWE-{match.group(1)}"
        
        return None

class SARDDatasetProcessor:
    """SARD数据集处理器"""
    
    def __init__(self, sard_data_dir: str = "sard_data"):
        self.sard_data_dir = sard_data_dir
        self.sard_base_url = "https://samate.nist.gov/SARD/"
        self.ensure_directory()
    
    def ensure_directory(self):
        """确保数据目录存在"""
        os.makedirs(self.sard_data_dir, exist_ok=True)
    
    def process_sard_testcases(self, max_records: int = 200) -> List[VulnerabilityRecord]:
        """
        处理SARD测试用例
        """
        logger.info("开始处理SARD数据集...")
        records = []
        
        # 为每个CWE类别生成示例记录
        for cwe_id, code_examples in self.cwe_examples.items():
            for i, code in enumerate(code_examples):
                if len(records) >= max_records:
                    break
                
                record = VulnerabilityRecord(
                    file_name=f"sard_testcase_{cwe_id.lower()}_{i+1}.c",
                    CWE_ID=cwe_id,
                    If_Vul=True,
                    Code=code.strip(),
                    Embedding=[]  # 空列表，预留字段
                )
                records.append(record)
                logger.info(f"生成SARD记录: {record.file_name} ({record.CWE_ID})")

        if len(records) < max_records:
            additional_records = self._generate_additional_samples(max_records - len(records))
            records.extend(additional_records)

        return records[:max_records]

class VulnerabilityDatabaseBuilder:

    
    def __init__(self, github_token: str = None):
        self.github_crawler = GitHubVulnerabilityCrawler(github_token)
        self.sard_processor = SARDDatasetProcessor()
        
    def build_database(self, output_file: str = "vulnerability_database.json",
                      github_records: int = 300, sard_records: int = 200) -> Dict:

        all_records = []
        stats = {
            'total_records': 0,
            'github_records': 0,
            'sard_records': 0,
            'cwe_distribution': {},
            'language_distribution': {}
        }
        
        try:
            # 从GitHub获取数据
            logger.info("从GitHub获取漏洞数据...")
            github_records_list = self.github_crawler.search_vulnerability_commits(
                languages=['C', 'C++', 'Java', 'Python', 'JavaScript'],
                max_results=github_records
            )
            all_records.extend(github_records_list)
            stats['github_records'] = len(github_records_list)
            
            # 从SARD获取数据
            logger.info("从SARD获取漏洞数据...")
            sard_records_list = self.sard_processor.process_sard_testcases(
                max_records=sard_records
            )
            all_records.extend(sard_records_list)
            stats['sard_records'] = len(sard_records_list)
            
            # 统计信息
            for record in all_records:
                # CWE分布
                cwe_id = record.CWE_ID
                stats['cwe_distribution'][cwe_id] = stats['cwe_distribution'].get(cwe_id, 0) + 1
                file_ext = os.path.splitext(record.file_name)[1].lower()
                ext_to_lang = {
                    '.c': 'C', '.h': 'C',

                }
                language = ext_to_lang.get(file_ext, 'Other')
                stats['language_distribution'][language] = stats['language_distribution'].get(language, 0) + 1
            
            stats['total_records'] = len(all_records)
            
            # 保存到JSON文件
            logger.info(f"保存数据库到 {output_file}...")
            database = {
                'metadata': {
                    'created_time': datetime.now().isoformat(),
                    'sources': ['GitHub', 'SARD'],
                    'description': '历史漏洞数据库，包含来自GitHub和SARD的漏洞代码片段'
                },
                'statistics': stats,
                'records': [asdict(record) for record in all_records]
            }
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(database, f, indent=2, ensure_ascii=False)
            

            return stats
            
        except Exception as e:
            logger.error(f"构建失败: {e}")
            raise

def main():
    """主函数"""

    github_token = os.getenv('GITHUB_TOKEN')
    
    # 创建数据库构建器
    builder = VulnerabilityDatabaseBuilder(github_token)
    
    # 构建数据库
    try:
        stats = builder.build_database(
            output_file="vulnerability_database.json",
            github_records=200,  
            sard_records=100    
        )
        
        
    except Exception as e:
        logger.error(f"执行失败: {e}")

if __name__ == "__main__":
    main()