import json
from transformers import Roberta<PERSON>okenizer, RobertaModel
import torch

# 加载 CodeBERT 模型
MODEL_NAME = "../codebert-base"
tokenizer = RobertaTokenizer.from_pretrained(MODEL_NAME)
model = RobertaModel.from_pretrained(MODEL_NAME)


from tqdm import tqdm

def add_codebert_embeddings_to_json(json_file_path, output_json_file_path):

    with open(json_file_path, 'r', encoding='utf-8') as file:
        data = json.load(file)  # 解析 JSON 数据

       
        if not isinstance(data, list):
            print("输入 JSON 文件的数据类型错误，期望是一个列表！")
            return

        with tqdm(total=len(data), desc="Processing Entries", unit="entry") as pbar:

            for i, item in enumerate(data):
                if "code" not in item:
                    pbar.update(1)  # 无代码字段，进度条更新
                    continue  # 跳过没有 code 字段的条目

                code_snippet = item["code"]

                # 使用 CodeBERT Tokenizer 对代码进行编码
                inputs = tokenizer(
                    code_snippet,
                    return_tensors="pt",
                    padding=True,
                    truncation=True,
                    max_length=512  # 限制最大 512 Token
                )

                # 通过 CodeBERT 模型生成嵌入
                with torch.no_grad():
                    outputs = model(**inputs)
                    embedding = outputs.last_hidden_state[:, 0, :].squeeze()  # 提取 [CLS] token 的嵌入

                # 将嵌入转换为 Python 列表，并添加到条目
                item["codebert_embedding"] = embedding.tolist()

                # 更新进度条
                pbar.update(1)

    # 将更新后的数据保存到新的 JSON 文件中
    with open(output_json_file_path, 'w', encoding='utf-8') as file:
        json.dump(data, file, ensure_ascii=False, indent=4)
        print(f"\n嵌入已添加并保存到文件：{output_json_file_path}")


input_json_file_path = "analyzed_files.json"  # 输入 JSON 文件路径
output_json_file_path = "analyzed_files_with_embeddings.json"  # 输出 JSON 文件路径
add_codebert_embeddings_to_json(input_json_file_path, output_json_file_path)

