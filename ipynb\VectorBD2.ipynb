{"cells": [{"cell_type": "code", "execution_count": null, "id": "9bfa77e6-80aa-486c-bb8a-d848be7efec1", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Some weights of RobertaModel were not initialized from the model checkpoint at ../graphcodebert-base and are newly initialized: ['roberta.pooler.dense.bias', 'roberta.pooler.dense.weight']\n", "You should probably TRAIN this model on a down-stream task to be able to use it for predictions and inference.\n", "Processing batches: 100%|██████████| 193/193 [1:51:20<00:00, 34.62s/batch]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["索引已保存至 cosine_similarity_index.faiss，元数据保存至 cosine_similarity_index.faiss.metadata\n"]}], "source": ["import faiss\n", "import numpy as np\n", "import json\n", "from transformers import RobertaTokenizer, RobertaModel\n", "import torch\n", "from tqdm import tqdm\n", "\n", "# 加载模型\n", "MODEL_NAME = \"../graphcodebert-base\"\n", "tokenizer = RobertaTokenizer.from_pretrained(MODEL_NAME)\n", "model = RobertaModel.from_pretrained(MODEL_NAME)\n", "\n", "def normalize(vectors):\n", "    \"\"\"\n", "    对嵌入向量进行归一化，使其适用于余弦相似度\n", "    :param vectors: np.n<PERSON>ray, shape = (n, d)\n", "    :return: np.n<PERSON><PERSON>, shape = (n, d), 行向量归一化后的向量\n", "    \"\"\"\n", "    norms = np.linalg.norm(vectors, axis=1, keepdims=True)  # 计算每个向量的 L2 范数\n", "    normalized_vectors = vectors / norms\n", "    return normalized_vectors\n", "\n", "def build_faiss_index_cosine_similarity(input_json_file, index_file_path, batch_size=1000):\n", "    \"\"\"\n", "    构建基于余弦相似度的 FAISS 索引\n", "    :param input_json_file: JSON 文件路径，包含代码块及其他元数据信息\n", "    :param index_file_path: 保存索引文件的路径\n", "    :param batch_size: 每批处理的代码条目数量\n", "    \"\"\"\n", "    # 打开 JSON 文件\n", "    with open(input_json_file, 'r', encoding='utf-8') as file:\n", "        data = json.load(file)\n", "\n", "    # 提取数据数量和维度信息\n", "    num_items = len(data)\n", "    \n", "    # 初始化元数据和索引\n", "    metadata = []\n", "    dimension = 768  # CodeBERT 嵌入的维度\n", "    index = faiss.IndexFlatIP(dimension)  # 用来模拟余弦相似度\n", "\n", "    # 分批处理数据并存入索引\n", "    for start_idx in tqdm(range(0, num_items, batch_size), desc=\"Processing batches\", unit=\"batch\"):\n", "        batch = data[start_idx:start_idx + batch_size]\n", "        \n", "        batch_embeddings = []\n", "        for item in batch:\n", "            if \"code\" not in item:\n", "                continue  # 跳过无代码片段的数据\n", "\n", "            # Tokenizer 编码\n", "            inputs = tokenizer(\n", "                item[\"code\"],\n", "                return_tensors=\"pt\",\n", "                truncation=True,\n", "                max_length=512\n", "            )\n", "\n", "            with torch.no_grad():\n", "                outputs = model(**inputs)\n", "                embedding = outputs.last_hidden_state[:, 0, :].squeeze()  # 提取 [CLS] token 的嵌入\n", "                batch_embeddings.append(embedding.numpy())\n", "                metadata.append({\n", "                    \"file_name\": item[\"file_name\"],\n", "                    \"CWE_ID\": item[\"CWE_ID\"],\n", "                    \"IF_VUL\": item[\"IF_VUL\"],\n", "                    \"code\": item[\"code\"]\n", "                })\n", "\n", "        # 转换嵌入为 NumPy 数组并归一化\n", "        if batch_embeddings:\n", "            batch_np = np.array(batch_embeddings, dtype=np.float32)\n", "            normalized_batch_np = normalize(batch_np)  # 对嵌入进行归一化\n", "            index.add(normalized_batch_np)  # 添加归一化向量到索引\n", "\n", "    # 保存索引到文件\n", "    faiss.write_index(index, index_file_path)\n", "\n", "    # 保存元数据到文件\n", "    with open(f\"{index_file_path}.metadata\", 'w', encoding='utf-8') as file:\n", "        json.dump(metadata, file, ensure_ascii=False, indent=4)\n", "\n", "    print(f\"索引已保存至 {index_file_path}，元数据保存至 {index_file_path}.metadata\")\n", "\n", "\n", "input_json_file = \"analyzed_files_with_graphcodebert_embeddings.json\"\n", "index_file_path = \"cosine_similarity_index.faiss\"\n", "build_faiss_index_cosine_similarity(input_json_file, index_file_path, batch_size=1000)\n"]}, {"cell_type": "code", "execution_count": null, "id": "c57b8fb5-f3e5-4a93-a325-f43aa03d1685", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.8"}}, "nbformat": 4, "nbformat_minor": 5}