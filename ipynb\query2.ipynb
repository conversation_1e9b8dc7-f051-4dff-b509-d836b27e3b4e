{"cells": [{"cell_type": "code", "execution_count": null, "id": "8bcbcda9-a9b4-4033-88f3-e0899daa89d1", "metadata": {"editable": true, "scrolled": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Some weights of RobertaModel were not initialized from the model checkpoint at ../graphcodebert-base and are newly initialized: ['roberta.pooler.dense.bias', 'roberta.pooler.dense.weight']\n", "You should probably TRAIN this model on a down-stream task to be able to use it for predictions and inference.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "=== Preprocessed Code ===\n", "int main(int argc, const char* argv[])\n", "{\n", "if (argc != argc) {\n", "return 1;\n", "}\n", "}\n", "Rank 1:\n", "  文件名: CWE15_External_Control_of_System_or_Configuration_Setting__w32_52c.c\n", "  CWE_ID: CWE-15\n", "  是否为漏洞: True\n", "  距离: 10.123437881469727\n", "  代码片段: void func_1(char * data)\n", "{\n", "if (!SetComputerNameA(data))\n", "{\n", "\n", "exit(1);\n", "}\n", "}\n", "\n", "Rank 2:\n", "  文件名: CWE15_External_Control_of_System_or_Configuration_Setting__w32_51b.c\n", "  CWE_ID: CWE-15\n", "  是否为漏洞: False\n", "  距离: 10.123437881469727\n", "  代码片段: void func_1(char * data)\n", "{\n", "if (!SetComputerNameA(data))\n", "{\n", "\n", "exit(1);\n", "}\n", "}\n", "\n", "Rank 3:\n", "  文件名: CWE15_External_Control_of_System_or_Configuration_Setting__w32_51b.c\n", "  CWE_ID: CWE-15\n", "  是否为漏洞: True\n", "  距离: 10.123437881469727\n", "  代码片段: void func_1(char * data)\n", "{\n", "if (!SetComputerNameA(data))\n", "{\n", "\n", "exit(1);\n", "}\n", "}\n", "\n", "Rank 4:\n", "  文件名: CWE15_External_Control_of_System_or_Configuration_Setting__w32_65b.c\n", "  CWE_ID: CWE-15\n", "  是否为漏洞: False\n", "  距离: 10.123437881469727\n", "  代码片段: void func_1(char * data)\n", "{\n", "if (!SetComputerNameA(data))\n", "{\n", "\n", "exit(1);\n", "}\n", "}\n", "\n", "Rank 5:\n", "  文件名: CWE15_External_Control_of_System_or_Configuration_Setting__w32_65b.c\n", "  CWE_ID: CWE-15\n", "  是否为漏洞: True\n", "  距离: 10.123437881469727\n", "  代码片段: void func_1(char * data)\n", "{\n", "if (!SetComputerNameA(data))\n", "{\n", "\n", "exit(1);\n", "}\n", "}\n", "\n", "Top 3 results have been saved to top_results.json\n"]}], "source": ["import re\n", "\n", "def clean_code_block(code_block):\n", "\n", "    if not isinstance(code_block, str) or not code_block.strip():\n", "        return \"\"\n", "\n", "    try:\n", "        # Remove single-line comments (// ...)\n", "        code_block = re.sub(r\"//.*\", \"\", code_block)\n", "        # Remove multi-line comments (/* ... */)\n", "        code_block = re.sub(r\"/\\*[\\s\\S]*?\\*/\", \"\", code_block)\n", "        \n", "        # Strip extra whitespace lines\n", "        code_block = \"\\n\".join([line.strip() for line in code_block.splitlines() if line.strip()])\n", "        return code_block\n", "    except Exception as e:\n", "        raise ValueError(f\"Error cleaning code block: {e}\")\n", "\n", "def anonymize_cwe_functions(code_block):\n", "    \"\"\"\n", "    Anonymize all functions or identifiers with 'CWE' in their name.\n", "    \"\"\"\n", "    if not isinstance(code_block, str) or not code_block.strip():\n", "        return \"\"\n", "\n", "    try:\n", "        placeholder_mapping = {}  # Map to store function name and its placeholder\n", "        function_pattern = r\"\\b(CWE\\w+)\\b\"  # Match functions or identifiers starting with CWE\n", "\n", "        def replace_function_name(match):\n", "            \"\"\"\n", "            Replace matched CWE function with a consistent placeholder.\n", "            \"\"\"\n", "            function_name = match.group(1)\n", "            if function_name not in placeholder_mapping:\n", "                placeholder_mapping[function_name] = f\"func_{len(placeholder_mapping) + 1}\"\n", "            return placeholder_mapping[function_name]\n", "\n", "        # Replace all CWE-prefixed functions with placeholders\n", "        anonymized_code = re.sub(function_pattern, replace_function_name, code_block)\n", "        return anonymized_code\n", "    except Exception as e:\n", "        raise ValueError(f\"Error anonymizing CWE functions: {e}\")\n", "\n", "def preprocess_code(code_block):\n", "    \"\"\"\n", "    Preprocess the code block by cleaning it and anonymizing CWE functions.\n", "\n", "    Args:\n", "        code_block (str): The input code block.\n", "\n", "    Returns:\n", "        str: The preprocessed code block.\n", "    \"\"\"\n", "    # Step 1: Clean the code block\n", "    cleaned_code = clean_code_block(code_block)\n", "    # Step 2: Anonymize CWE-prefixed functions\n", "    anonymized_code = anonymize_cwe_functions(cleaned_code)\n", "    return anonymized_code\n", "    \n", "\n", "\n", "\n", "\n", "import faiss\n", "import numpy as np\n", "import json\n", "def search_faiss_index_flat(query_embedding, index_file_path, top_k=5):\n", "    \"\"\"\n", "    使用 FlatL2 索引对查询嵌入进行检索\n", "    :param query_embedding: 输入的查询嵌入 (1D NumPy 数组)\n", "    :param index_file_path: FAISS 索引文件路径\n", "    :param top_k: 返回的最近邻条目的数量\n", "    :return: 检索到的元数据结果\n", "    \"\"\"\n", "    # 加载索引\n", "    index = faiss.read_index(index_file_path)\n", "\n", "    # 加载元数据\n", "    with open(index_file_path + \".metadata\", 'r', encoding='utf-8') as file:\n", "        metadata = json.load(file)\n", "\n", "    # 将查询嵌入转换为 NumPy 格式\n", "    query_np = np.array([query_embedding], dtype=np.float32)\n", "\n", "    # 检索最近邻\n", "    distances, indices = index.search(query_np, top_k)\n", "\n", "    # 映射查询结果到元数据\n", "    results = []\n", "    for i in range(top_k):\n", "        idx = indices[0][i]\n", "        if idx < len(metadata):\n", "            result = metadata[idx]\n", "            result[\"distance\"] = distances[0][i]\n", "            results.append(result)\n", "\n", "    return results\n", "\n", "\n", "from transformers import RobertaTokenizer, RobertaModel\n", "import torch\n", "\n", "# 加载 CodeBERT 模型和 Tokenizer\n", "LOCAL_MODEL_DIR = \"../graphcodebert-base\"\n", "tokenizer = RobertaTokenizer.from_pretrained(LOCAL_MODEL_DIR)\n", "model = RobertaModel.from_pretrained(LOCAL_MODEL_DIR)\n", "\n", "# 输入查询代码\n", "query_code = \"\"\"\n", "\n", "int main(int argc, const char* argv[])\n", "{\n", "  if (argc != argc) {\n", "    return 1;\n", "  }\n", "}\n", "\"\"\"\n", "\n", "# Step 3: 链式处理\n", "preprocessed_code = preprocess_code(query_code)\n", "print(\"\\n=== Preprocessed Code ===\")\n", "print(preprocessed_code)\n", "\n", "\n", "\n", "# 将代码转化为嵌入\n", "inputs = tokenizer(preprocessed_code, return_tensors=\"pt\", truncation=True, max_length=512)\n", "with torch.no_grad():\n", "    outputs = model(**inputs)\n", "    query_embedding = outputs.last_hidden_state[:, 0, :].squeeze().numpy()\n", "\n", "# 使用索引检索\n", "results = search_faiss_index_flat(query_embedding, \"cosine_similarity_index.faiss\", top_k=5)\n", "\n", "# 打印检索结果\n", "for i, result in enumerate(results):\n", "    print(f\"Rank {i + 1}:\")\n", "    print(f\"  文件名: {result['file_name']}\")\n", "    print(f\"  CWE_ID: {result['CWE_ID']}\")\n", "    print(f\"  是否为漏洞: {result['IF_VUL']}\")\n", "    print(f\"  距离: {result['distance']}\")\n", "    print(f\"  代码片段: {result['code']}\\n\")\n", "\n", "for result in results:\n", "    result['distance'] = float(result['distance'])\n", "top_results = results[:2]\n", "\n", "# 存储到 JSON 文件\n", "output_file = 'top_results.json'\n", "\n", "try:\n", "    with open(output_file, 'w', encoding='utf-8') as f:\n", "        json.dump(top_results, f, indent=4, ensure_ascii=False)\n", "    print(f\"Top 3 results have been saved to {output_file}\")\n", "except Exception as e:\n", "    print(f\"Error while writing JSON file: {e}\")"]}, {"cell_type": "code", "execution_count": null, "id": "c30b77b5-dfb2-439c-afb4-63ea412de9b6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["int main(int argc, const char* argv[])\n", "{\n", "if (argc != argc) {\n", "return 1;\n", "}\n", "}\n", "代码已写入 ./SAST/main.cpp\n"]}], "source": ["import os\n", "\n", "def write_to_main_cpp(preprocessed_code, output_dir=\"./SAST\"):\n", "    \"\"\"\n", "    Args:\n", "        preprocessed_code (str): 预处理的代码。\n", "        output_dir (str): PVS-Studio 项目所在的目录\n", "    \"\"\"\n", "    if not os.path.exists(output_dir):\n", "        os.makedirs(output_dir)\n", "\n", "    # 创建并写入 main.cpp 文件\n", "    main_cpp_path = os.path.join(output_dir, \"main.cpp\")\n", "    with open(main_cpp_path, 'w') as f:\n", "        # 添加 PVS-Studio 识别的开头注释\n", "        f.write(\"// This is an open source non-commercial project. Dear PVS-Studio, please check it.\\n\")\n", "        f.write(\"// PVS-Studio Static Code Analyzer for C, C++ and C#: http://www.viva64.com\\n\\n\")\n", " \n", "        f.write(preprocessed_code)  # 插入预处理的代码\n", "        f.write(\"}\\n\")\n", "\n", "    print(f\"代码已写入 {main_cpp_path}\")\n", "\n", "# `preprocessed_code` 是从上一个代码块得到的预处理代码\n", "print(preprocessed_code)\n", "write_to_main_cpp(preprocessed_code)\n"]}, {"cell_type": "code", "execution_count": null, "id": "3a4d344d-58d0-4988-8c6d-f7e1af3e7d98", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Checking directory: /root/preprocess/SAST/build\n", "Running CMake...\n", "-- Configuring done\n", "-- Generating done\n", "-- Build files have been written to: /root/preprocess/SAST/build\n", "Building and running PVS-Studio analysis...\n", "\u001b[35m\u001b[1mScanning dependencies of target example1.analyze-pvs_analysis.xml-log\u001b[0m\n", "[ 25%] \u001b[34m\u001b[1mAnalyzing CXX file main.cpp\u001b[0m\n", "[ 50%] \u001b[34m\u001b[1mGenerating pvs_analysis.xml\u001b[0m\n", "Analyzer log conversion tool.\n", "Copyright (c) 2025 PVS-Studio LLC\n", "\n", "PVS-Studio is a static code analyzer and SAST (static application security\n", "testing) tool that is available for C and C++ desktop and embedded development,\n", "C# and Java under Windows, Linux and macOS.\n", "\n", "<?xml version=\"1.0\"?>\n", "<NewDataSet>\n", "  <PVS-Studio_Analysis_Log>\n", "    <Level>1</Level>\n", "    <ErrorType>error</ErrorType>\n", "    <ErrorCode>V501</ErrorCode>\n", "    <Message>There are identical sub-expressions to the left and to the right of the '!=' operator: argc != argc</Message>\n", "    <Line>6</Line>\n", "    <File>/root/preprocess/SAST/main.cpp</File>\n", "    <CWECode>CWE-570</CWECode>\n", "  </PVS-Studio_Analysis_Log>\n", "</NewDataSet>\n", "Total messages: 2\n", "Filtered messages: 1\n", "[ 50%] Built target example1.analyze-pvs_analysis.xml-log\n", "\u001b[35m\u001b[1mConsolidate compiler generated dependencies of target pvs-studio-cmake-example-1\u001b[0m\n", "[ 75%] \u001b[32mBuilding CXX object CMakeFiles/pvs-studio-cmake-example-1.dir/main.cpp.o\u001b[0m\n", "Error during CMake build or analysis: Command '['cmake', '--build', '.', '--target', 'example1.analyze']' returned non-zero exit status 2.\n", "Analysis report generation failed.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/root/preprocess/SAST/main.cpp:9:2: error: expected declaration before ‘}’ token\n", "    9 | }}\n", "      |  ^\n", "gmake[3]: *** [CMakeFiles/pvs-studio-cmake-example-1.dir/build.make:76: CMakeFiles/pvs-studio-cmake-example-1.dir/main.cpp.o] Error 1\n", "gmake[2]: *** [CMakeFiles/Makefile2:86: CMakeFiles/pvs-studio-cmake-example-1.dir/all] Error 2\n", "gmake[1]: *** [CMakeFiles/Makefile2:146: CMakeFiles/example1.analyze.dir/rule] Error 2\n", "gmake: *** [Makefile:150: example1.analyze] Error 2\n"]}], "source": ["import subprocess\n", "import os\n", "import xml.etree.ElementTree as ET\n", "\n", "def run_cmake_and_analyze(project_dir):\n", "    \"\"\"\n", "    执行 CMake 构建和 PVS-Studio 静态分析，并读取生成的 XML 报告。\n", "    \n", "    Args:\n", "        project_dir (str): PVS-Studio 项目的目录（包含 CMakeLists.txt 和源代码）。\n", "    \n", "    Returns:\n", "        str: 分析报告的路径。\n", "    \"\"\"\n", "    try:\n", "        # 获取绝对路径并检查目录是否存在\n", "        abs_project_dir = os.path.abspath(project_dir)\n", "        print(f\"Checking directory: {abs_project_dir}\")\n", "        \n", "        if not os.path.exists(abs_project_dir):\n", "            raise FileNotFoundError(f\"The directory {abs_project_dir} does not exist.\")\n", "\n", "        # 进入项目目录\n", "        os.chdir(abs_project_dir)\n", "\n", "        # 执行 CMake 配置\n", "        print(\"Running CMake...\")\n", "        subprocess.check_call(['cmake', '.'])\n", "\n", "        # 执行构建过程并进行静态分析\n", "        print(\"Building and running PVS-Studio analysis...\")\n", "        subprocess.check_call(['cmake', '--build', '.', '--target', 'example1.analyze'])\n", "        \n", "        # 获取报告文件的路径\n", "        report_path = os.path.join(os.getcwd(), 'pvs_analysis.xml')\n", "        \n", "        if not os.path.exists(report_path):\n", "            raise FileNotFoundError(f\"PVS-Studio analysis report not found at {report_path}\")\n", "\n", "        return report_path\n", "\n", "    except subprocess.CalledProcessError as e:\n", "        print(f\"Error during CMake build or analysis: {e}\")\n", "        return None\n", "\n", "\n", "\n", "project_dir = \"./SAST/build\"  # 项目目录\n", "\n", "# 运行 CMake 并生成报告\n", "report_path = run_cmake_and_analyze(project_dir)\n", "\n", "# 输出报告路径\n", "if report_path:\n", "    print(f\"Analysis report generated at: {report_path}\")\n", "else:\n", "    print(\"Analysis report generation failed.\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "0d0582c4-bdd1-47fb-b666-1f230b08f00d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Error Type: error\n", "Error Code: V501\n", "Message: There are identical sub-expressions to the left and to the right of the '!=' operator: argc != argc\n", "File: /root/preprocess/SAST/main.cpp\n", "Line: 6\n", "CWE Code: CWE-570\n", "--------------------------------------------------\n"]}], "source": ["import xml.etree.ElementTree as ET\n", "\n", "def parse_pvs_analysis_report(report_path):\n", "    \"\"\"\n", "    解析 PVS-Studio 静态分析报告，并输出相关信息。\n", "    \n", "    Args:\n", "        report_path (str): PVS-Studio 分析报告的路径（XML 文件）。\n", "    \"\"\"\n", "    try:\n", "        # 解析 XML 文件\n", "        tree = ET.parse(report_path)\n", "        root = tree.getroot()\n", "\n", "        # 遍历所有 <PVS-Studio_Analysis_Log> 元素\n", "        for log_entry in root.findall('.//PVS-Studio_Analysis_Log'):\n", "            # 提取每个问题的信息\n", "            error_type = log_entry.find('ErrorType').text if log_entry.find('ErrorType') is not None else 'N/A'\n", "            error_code = log_entry.find('ErrorCode').text if log_entry.find('ErrorCode') is not None else 'N/A'\n", "            message = log_entry.find('Message').text if log_entry.find('Message') is not None else 'N/A'\n", "            line = log_entry.find('Line').text if log_entry.find('Line') is not None else 'N/A'\n", "            file_name = log_entry.find('File').text if log_entry.find('File') is not None else 'N/A'\n", "            cwe_code = log_entry.find('CWECode').text if log_entry.find('CWECode') is not None else 'N/A'\n", "\n", "            # 输出每个问题的详细信息\n", "            print(f\"Error Type: {error_type}\")\n", "            print(f\"Error Code: {error_code}\")\n", "            print(f\"Message: {message}\")\n", "            print(f\"File: {file_name}\")\n", "            print(f\"Line: {line}\")\n", "            print(f\"CWE Code: {cwe_code}\")\n", "            print(\"-\" * 50)\n", "\n", "    except Exception as e:\n", "        print(f\"Error parsing the PVS-Studio report: {e}\")\n", "\n", "\n", "report_path = \"pvs_analysis.xml\"  # 替换为实际的报告路径\n", "\n", "# 解析并输出分析报告内容\n", "parse_pvs_analysis_report(report_path)"]}, {"cell_type": "code", "execution_count": 2, "id": "fb8eb1c6-93a5-4ff6-b4ba-6c1e12bebc43", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Some weights of RobertaModel were not initialized from the model checkpoint at ../graphcodebert-base and are newly initialized: ['roberta.pooler.dense.bias', 'roberta.pooler.dense.weight']\n", "You should probably TRAIN this model on a down-stream task to be able to use it for predictions and inference.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "=== Preprocessed Code ===\n", "void fun()\n", "{  FILE * f;\n", "char buf[10];\n", "f = fopen(\"TestInputFile1\", \"r\");\n", "assert(f != NULL);\n", "fgets(buf, 11, f);\n", "fclose(f);\n", "}\n", "Rank 1:\n", "  文件名: CWE675_Duplicate_Operations_on_Resource__fopen_53a.c\n", "  CWE_ID: CWE-675\n", "  是否为漏洞: True\n", "  距离: 10.483758926391602\n", "  代码片段: void func_1(FILE * data);\n", "void func_2()\n", "{\n", "FILE * data;\n", "data = NULL;\n", "data = fopen(\"BadSource_fopen.txt\", \"w+\");\n", "fclose(data);\n", "func_1(data);\n", "}\n", "\n", "Rank 2:\n", "  文件名: CWE675_Duplicate_Operations_on_Resource__fopen_54a.c\n", "  CWE_ID: CWE-675\n", "  是否为漏洞: True\n", "  距离: 10.483758926391602\n", "  代码片段: void func_1(FILE * data);\n", "void func_2()\n", "{\n", "FILE * data;\n", "data = NULL;\n", "data = fopen(\"BadSource_fopen.txt\", \"w+\");\n", "fclose(data);\n", "func_1(data);\n", "}\n", "\n", "Rank 3:\n", "  文件名: CWE675_Duplicate_Operations_on_Resource__fopen_51a.c\n", "  CWE_ID: CWE-675\n", "  是否为漏洞: True\n", "  距离: 10.483758926391602\n", "  代码片段: void func_1(FILE * data);\n", "void func_2()\n", "{\n", "FILE * data;\n", "data = NULL;\n", "data = fopen(\"BadSource_fopen.txt\", \"w+\");\n", "fclose(data);\n", "func_1(data);\n", "}\n", "\n", "Rank 4:\n", "  文件名: CWE675_Duplicate_Operations_on_Resource__fopen_52a.c\n", "  CWE_ID: CWE-675\n", "  是否为漏洞: True\n", "  距离: 10.483758926391602\n", "  代码片段: void func_1(FILE * data);\n", "void func_2()\n", "{\n", "FILE * data;\n", "data = NULL;\n", "data = fopen(\"BadSource_fopen.txt\", \"w+\");\n", "fclose(data);\n", "func_1(data);\n", "}\n", "\n", "Rank 5:\n", "  文件名: CWE675_Duplicate_Operations_on_Resource__fopen_63a.c\n", "  CWE_ID: CWE-675\n", "  是否为漏洞: True\n", "  距离: 10.470474243164062\n", "  代码片段: void func_1(FILE * * dataPtr);\n", "void func_2()\n", "{\n", "FILE * data;\n", "data = NULL;\n", "data = fopen(\"BadSource_fopen.txt\", \"w+\");\n", "fclose(data);\n", "func_1(&data);\n", "}\n", "\n", "Top 3 results have been saved to top_results.json\n"]}], "source": ["import re\n", "\n", "def clean_code_block(code_block):\n", "\n", "    if not isinstance(code_block, str) or not code_block.strip():\n", "        return \"\"\n", "\n", "    try:\n", "        # Remove single-line comments (// ...)\n", "        code_block = re.sub(r\"//.*\", \"\", code_block)\n", "        # Remove multi-line comments (/* ... */)\n", "        code_block = re.sub(r\"/\\*[\\s\\S]*?\\*/\", \"\", code_block)\n", "        \n", "        # Strip extra whitespace lines\n", "        code_block = \"\\n\".join([line.strip() for line in code_block.splitlines() if line.strip()])\n", "        return code_block\n", "    except Exception as e:\n", "        raise ValueError(f\"Error cleaning code block: {e}\")\n", "\n", "def anonymize_cwe_functions(code_block):\n", "    \"\"\"\n", "    Anonymize all functions or identifiers with 'CWE' in their name.\n", "    \"\"\"\n", "    if not isinstance(code_block, str) or not code_block.strip():\n", "        return \"\"\n", "\n", "    try:\n", "        placeholder_mapping = {}  # Map to store function name and its placeholder\n", "        function_pattern = r\"\\b(CWE\\w+)\\b\"  # Match functions or identifiers starting with CWE\n", "\n", "        def replace_function_name(match):\n", "            \"\"\"\n", "            Replace matched CWE function with a consistent placeholder.\n", "            \"\"\"\n", "            function_name = match.group(1)\n", "            if function_name not in placeholder_mapping:\n", "                placeholder_mapping[function_name] = f\"func_{len(placeholder_mapping) + 1}\"\n", "            return placeholder_mapping[function_name]\n", "\n", "        # Replace all CWE-prefixed functions with placeholders\n", "        anonymized_code = re.sub(function_pattern, replace_function_name, code_block)\n", "        return anonymized_code\n", "    except Exception as e:\n", "        raise ValueError(f\"Error anonymizing CWE functions: {e}\")\n", "\n", "def preprocess_code(code_block):\n", "    \"\"\"\n", "    Preprocess the code block by cleaning it and anonymizing CWE functions.\n", "\n", "    Args:\n", "        code_block (str): The input code block.\n", "\n", "    Returns:\n", "        str: The preprocessed code block.\n", "    \"\"\"\n", "    # Step 1: Clean the code block\n", "    cleaned_code = clean_code_block(code_block)\n", "    # Step 2: Anonymize CWE-prefixed functions\n", "    anonymized_code = anonymize_cwe_functions(cleaned_code)\n", "    return anonymized_code\n", "    \n", "\n", "\n", "\n", "\n", "import faiss\n", "import numpy as np\n", "import json\n", "def search_faiss_index_flat(query_embedding, index_file_path, min_vul_count=3, top_k=5):\n", "    \"\"\"\n", "    使用 FlatL2 索引对查询嵌入进行检索，并确保至少返回 3 个 'IF_VUL' 为 True 的结果\n", "    :param query_embedding: 输入的查询嵌入 (1D NumPy 数组)\n", "    :param index_file_path: FAISS 索引文件路径\n", "    :param min_vul_count: 至少返回的 'IF_VUL' 为 True 的结果数\n", "    :param top_k: 每次检索的最近邻条目的数量\n", "    :return: 检索到的元数据结果\n", "    \"\"\"\n", "    # 加载索引\n", "    index = faiss.read_index(index_file_path)\n", "\n", "    # 加载元数据\n", "    with open(index_file_path + \".metadata\", 'r', encoding='utf-8') as file:\n", "        metadata = json.load(file)\n", "\n", "    # 将查询嵌入转换为 NumPy 格式\n", "    query_np = np.array([query_embedding], dtype=np.float32)\n", "\n", "    results = []\n", "    found_vulnerabilities = 0\n", "    current_k = top_k\n", "\n", "    while found_vulnerabilities < min_vul_count:\n", "        # 检索最近邻\n", "        distances, indices = index.search(query_np, current_k)\n", "\n", "        # 映射查询结果到元数据\n", "        for i in range(current_k):\n", "            idx = indices[0][i]\n", "            if idx < len(metadata):\n", "                result = metadata[idx]\n", "                result[\"distance\"] = distances[0][i]\n", "                results.append(result)\n", "                if result[\"IF_VUL\"] == True:\n", "                    found_vulnerabilities += 1\n", "\n", "        # 如果没有找到足够的漏洞结果，增加检索数量\n", "        if found_vulnerabilities < min_vul_count:\n", "            current_k *= 2  # Double the number of results to retrieve next time\n", "\n", "    # 返回检索到的最终结果\n", "    return results\n", "\n", "\n", "\n", "from transformers import RobertaTokenizer, RobertaModel\n", "import torch\n", "\n", "# 加载 CodeBERT 模型和 Tokenizer\n", "LOCAL_MODEL_DIR = \"../graphcodebert-base\"\n", "tokenizer = RobertaTokenizer.from_pretrained(LOCAL_MODEL_DIR)\n", "model = RobertaModel.from_pretrained(LOCAL_MODEL_DIR)\n", "\n", "# 输入查询代码（用户可能输入的文本）\n", "query_code = \"\"\"\n", "\n", "void fun()\n", "{  FILE * f;\n", "  char buf[10];\n", "\n", "  f = fopen(\"TestInputFile1\", \"r\");\n", "  assert(f != NULL);\n", "\n", "  /*  BAD  */\n", "  fgets(buf, 11, f);\n", "\n", "  fclose(f);\n", "\n", "\n", "}\n", "\"\"\"\n", "\n", "# Step 3: 链式处理\n", "preprocessed_code = preprocess_code(query_code)\n", "print(\"\\n=== Preprocessed Code ===\")\n", "print(preprocessed_code)\n", "\n", "\n", "\n", "# 将代码转化为嵌入\n", "inputs = tokenizer(preprocessed_code, return_tensors=\"pt\", truncation=True, max_length=512)\n", "with torch.no_grad():\n", "    outputs = model(**inputs)\n", "    query_embedding = outputs.last_hidden_state[:, 0, :].squeeze().numpy()\n", "\n", "# 使用索引检索\n", "results = search_faiss_index_flat(query_embedding, \"cosine_similarity_index.faiss\", top_k=5)\n", "\n", "# 打印检索结果\n", "for i, result in enumerate(results):\n", "    print(f\"Rank {i + 1}:\")\n", "    print(f\"  文件名: {result['file_name']}\")\n", "    print(f\"  CWE_ID: {result['CWE_ID']}\")\n", "    print(f\"  是否为漏洞: {result['IF_VUL']}\")\n", "    print(f\"  距离: {result['distance']}\")\n", "    print(f\"  代码片段: {result['code']}\\n\")\n", "\n", "for result in results:\n", "    result['distance'] = float(result['distance'])\n", "top_results = results[:5]\n", "\n", "# 存储到 JSON 文件\n", "output_file = 'top_results.json'\n", "\n", "try:\n", "    with open(output_file, 'w', encoding='utf-8') as f:\n", "        json.dump(top_results, f, indent=4, ensure_ascii=False)\n", "    print(f\"Top 3 results have been saved to {output_file}\")\n", "except Exception as e:\n", "    print(f\"Error while writing JSON file: {e}\")"]}, {"cell_type": "code", "execution_count": null, "id": "54348910-aa65-4ea7-9034-79bf3f137351", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.8"}}, "nbformat": 4, "nbformat_minor": 5}