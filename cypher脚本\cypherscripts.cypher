// Neo4j数据导入和查询Cypher脚本集合


// 1. 清空数据库
MATCH (n) DETACH DELETE n;

// 2. 创建索引以提高查询性能
CREATE INDEX method_name_index IF NOT EXISTS FOR (m:METHOD) ON (m.name);
CREATE INDEX method_filename_index IF NOT EXISTS FOR (m:METHOD) ON (m.filename);
CREATE INDEX call_name_index IF NOT EXISTS FOR (c:CALL) ON (c.name);
CREATE INDEX identifier_name_index IF NOT EXISTS FOR (i:IDENTIFIER) ON (i.name);
CREATE INDEX type_name_index IF NOT EXISTS FOR (t:TYPE) ON (t.name);
CREATE INDEX file_name_index IF NOT EXISTS FOR (f:FILE) ON (f.name);

// 3. 创建约束确保唯一性
CREATE CONSTRAINT method_id_unique IF NOT EXISTS FOR (m:METHOD) REQUIRE m.id IS UNIQUE;
CREATE CONSTRAINT call_id_unique IF NOT EXISTS FOR (c:CALL) REQUIRE c.id IS UNIQUE;
CREATE CONSTRAINT type_id_unique IF NOT EXISTS FOR (t:TYPE) REQUIRE t.id IS UNIQUE;
CREATE CONSTRAINT file_id_unique IF NOT EXISTS FOR (f:FILE) REQUIRE f.id IS UNIQUE;

// ==================== 批量数据导入脚本 ====================

// 4. 批量导入方法节点
UNWIND $methods AS method
CREATE (m:METHOD)
SET m = method.properties,
    m.id = method.id,
    m.label = method.label;

// 5. 批量导入函数调用节点
UNWIND $calls AS call
CREATE (c:CALL)
SET c = call.properties,
    c.id = call.id,
    c.label = call.label;

// 6. 批量导入类型节点
UNWIND $types AS type
CREATE (t:TYPE)
SET t = type.properties,
    t.id = type.id,
    t.label = type.label;

// 7. 批量导入文件节点
UNWIND $files AS file
CREATE (f:FILE)
SET f = file.properties,
    f.id = file.id,
    f.label = file.label;

// 8. 批量创建调用关系
UNWIND $call_relations AS rel
MATCH (m:METHOD {id: rel.source})
MATCH (c:CALL {id: rel.target})
CREATE (m)-[r:CALLS]->(c)
SET r = rel.properties;

// 9. 批量创建包含关系（方法包含调用）
UNWIND $contains_relations AS rel
MATCH (m:METHOD {id: rel.source})
MATCH (c:CALL {id: rel.target})
CREATE (m)-[r:CONTAINS]->(c)
SET r = rel.properties;

// 10. 批量创建数据流关系
UNWIND $dataflow_relations AS rel
MATCH (source {id: rel.source})
MATCH (target {id: rel.target})
CREATE (source)-[r:DATA_FLOW]->(target)
SET r = rel.properties;

// ==================== 依赖查询脚本 ====================

// 11. 查询变量定义位置
MATCH (n:IDENTIFIER {name: $var_name})
WHERE n.filename CONTAINS $file_path OR $file_path IS NULL
OPTIONAL MATCH (n)<-[:DEFINES]-(definer)
RETURN n.name as variable_name, 
       n.filename as file, 
       n.lineNumber as line, 
       n.columnNumber as column,
       definer.name as defined_in,
       definer.label as definer_type
ORDER BY n.lineNumber;

// 12. 查询函数调用关系
MATCH (m:METHOD)-[:CONTAINS]->(c:CALL)
WHERE m.filename = $file_path
OPTIONAL MATCH (target:METHOD {name: c.name})
RETURN c.name as function_name,
       c.lineNumber as call_line,
       m.name as caller_method,
       target.filename as target_file,
       target.lineNumber as target_line
ORDER BY c.lineNumber;

// 13. 查询外部函数调用（调用的函数在其他文件中定义）
MATCH (m:METHOD)-[:CONTAINS]->(c:CALL)
WHERE m.filename = $file_path
MATCH (target:METHOD {name: c.name})
WHERE target.filename <> m.filename
RETURN c.name as external_function,
       c.lineNumber as call_line,
       target.filename as defined_in_file,
       target.lineNumber as definition_line,
       target.signature as signature
ORDER BY c.lineNumber;

// 14. 查询类型定义信息
MATCH (t:TYPE)
WHERE t.name = $type_name OR t.fullName CONTAINS $type_name
OPTIONAL MATCH (t)-[:HAS_MEMBER]->(member)
RETURN t.name as type_name,
       t.fullName as full_name,
       t.filename as defined_in,
       collect(member.name) as members
ORDER BY t.name;

// 15. 查询文件包含关系
MATCH (f:FILE)-[:INCLUDES]->(included:FILE)
WHERE f.name = $file_path
RETURN f.name as file,
       collect(included.name) as included_files;

// 16. 查询被包含关系（哪些文件包含了当前文件）
MATCH (f:FILE)-[:INCLUDES]->(target:FILE)
WHERE target.name = $file_path
RETURN target.name as file,
       collect(f.name) as included_by;

// ==================== 高级查询脚本 ====================

// 17. 查询函数调用链（路径查询）
MATCH path = (start:METHOD {name: $start_function})-[:CALLS*1..5]->(end:METHOD {name: $end_function})
WHERE start.filename = $file_path
RETURN [node in nodes(path) | node.name] as call_chain,
       length(path) as chain_length
ORDER BY chain_length;

// 18. 查询数据依赖关系
MATCH (source)-[:DATA_FLOW*1..3]->(target:IDENTIFIER {name: $variable_name})
WHERE source.filename = $file_path
RETURN source.name as data_source,
       source.lineNumber as source_line,
       target.name as target_variable,
       target.lineNumber as target_line;

// 19. 查询控制流图
MATCH (m:METHOD {name: $method_name})-[:CFG_NEXT*]->(block)
WHERE m.filename = $file_path
RETURN m.name as method,
       collect(block.lineNumber) as control_flow_lines
ORDER BY block.lineNumber;

// 20. 查询方法的所有外部依赖
MATCH (m:METHOD {name: $method_name, filename: $file_path})
MATCH (m)-[:CONTAINS]->(c:CALL)
MATCH (external