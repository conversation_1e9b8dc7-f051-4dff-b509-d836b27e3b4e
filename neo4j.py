import os
import json
import subprocess
import logging
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
from dataclasses import dataclass
from neo4j import GraphDatabase
import tree_sitter
from tree_sitter import Language, Parser

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class GraphNode:
    """图节点数据结构"""
    id: str
    label: str
    properties: Dict[str, Any]

@dataclass
class GraphEdge:
    """图边数据结构"""
    source: str
    target: str
    relationship: str
    properties: Dict[str, Any]

@dataclass
class ExternalReference:
    """外部引用信息"""
    name: str
    type: str  # function, variable, type
    file_path: str
    line_number: int
    context: str

class JoernAnalyzer:
    """Joern代码分析器"""
    
    def __init__(self, joern_path: str = "/opt/joern"):
        self.joern_path = joern_path
        self.workspace_path = "/tmp/joern_workspace"
        os.makedirs(self.workspace_path, exist_ok=True)
        
    def analyze_repository(self, repo_path: str) -> str:

        try:
            logger.info(f"开始分析代码仓库: {repo_path}")
            
            # 构建Joern命令
            cpg_output = os.path.join(self.workspace_path, "cpg.json")
            
            # 使用Joern的c2cpg工具分析C代码
            cmd = [
                os.path.join(self.joern_path, "c2cpg"),
                "--output", cpg_output,
                repo_path
            ]
            
            # 执行Joern分析
            result = subprocess.run(
                cmd, 
                capture_output=True, 
                text=True, 
                timeout=300
            )
            
            if result.returncode != 0:
                raise Exception(f"Joern分析失败: {result.stderr}")
                
            logger.info("Joern分析完成")
            return cpg_output
            
        except Exception as e:
            logger.error(f"代码分析失败: {str(e)}")
            raise
    
    def extract_graph_data(self, cpg_path: str) -> Tuple[List[GraphNode], List[GraphEdge]]:
    
        try:

            queries = {
                "methods": "cpg.method.toJson",
                "calls": "cpg.call.toJson", 
                "identifiers": "cpg.identifier.toJson",
                "types": "cpg.typeDecl.toJson",
                "files": "cpg.file.toJson"
            }
            
            nodes = []
            edges = []
            
            for query_name, query in queries.items():
                result = self._execute_joern_query(cpg_path, query)
                extracted_nodes, extracted_edges = self._parse_query_result(
                    result, query_name
                )
                nodes.extend(extracted_nodes)
                edges.extend(extracted_edges)
            
            # 提取函数调用关系
            call_edges = self._extract_call_relationships(cpg_path)
            edges.extend(call_edges)
            
            logger.info(f"提取完成: {len(nodes)}个节点, {len(edges)}条边")
            return nodes, edges
            
        except Exception as e:
            logger.error(f"图数据提取失败: {str(e)}")
            raise
    
    def _execute_joern_query(self, cpg_path: str, query: str) -> str:
        """执行Joern查询"""
        cmd = [
            os.path.join(self.joern_path, "joern"),
            "--script", f"{query}",
            "--cpg", cpg_path
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode != 0:
            logger.warning(f"查询执行警告: {result.stderr}")
        
        return result.stdout
    
    def _parse_query_result(self, result: str, query_type: str) -> Tuple[List[GraphNode], List[GraphEdge]]:
        """解析查询结果"""
        nodes = []
        edges = []
        
        try:
            if not result.strip():
                return nodes, edges
                
            data = json.loads(result)
            
            for item in data:
                if query_type == "methods":
                    node = GraphNode(
                        id=f"method_{item.get('id', '')}",
                        label="METHOD",
                        properties={
                            "name": item.get("name", ""),
                            "signature": item.get("signature", ""),
                            "filename": item.get("filename", ""),
                            "lineNumber": item.get("lineNumber", 0),
                            "columnNumber": item.get("columnNumber", 0)
                        }
                    )
                    nodes.append(node)
                    
                elif query_type == "calls":
                    node = GraphNode(
                        id=f"call_{item.get('id', '')}",
                        label="CALL",
                        properties={
                            "name": item.get("name", ""),
                            "methodFullName": item.get("methodFullName", ""),
                            "filename": item.get("filename", ""),
                            "lineNumber": item.get("lineNumber", 0)
                        }
                    )
                    nodes.append(node)
                    
                elif query_type == "types":
                    node = GraphNode(
                        id=f"type_{item.get('id', '')}",
                        label="TYPE",
                        properties={
                            "name": item.get("name", ""),
                            "fullName": item.get("fullName", ""),
                            "filename": item.get("filename", "")
                        }
                    )
                    nodes.append(node)
                    
        except json.JSONDecodeError:
            logger.warning(f"无法解析{query_type}查询结果")
        
        return nodes, edges
    
    def _extract_call_relationships(self, cpg_path: str) -> List[GraphEdge]:
        """提取函数调用关系"""
        edges = []
        
        # 查询函数调用关系
        query = """
        cpg.call.map(call => 
            Json.obj(
                "callId" -> call.id,
                "callerMethod" -> call.method.id,
                "calleeName" -> call.name
            )
        ).toJson
        """
        
        result = self._execute_joern_query(cpg_path, query)
        
        try:
            if result.strip():
                data = json.loads(result)
                for item in data:
                    edge = GraphEdge(
                        source=f"method_{item.get('callerMethod', '')}",
                        target=f"call_{item.get('callId', '')}",
                        relationship="CALLS",
                        properties={
                            "calleeName": item.get("calleeName", "")
                        }
                    )
                    edges.append(edge)
        except json.JSONDecodeError:
            logger.warning("无法解析函数调用关系")
        
        return edges

class TreeSitterParser:
    """Tree-Sitter代码解析器"""
    
    def __init__(self, language_path: str = None):
        if language_path:
            # 加载C语言解析器
            C_LANGUAGE = Language(language_path, 'c')
            self.parser = Parser()
            self.parser.set_language(C_LANGUAGE)
        else:
            self.parser = None
    
    def extract_external_references(self, file_path: str) -> List[ExternalReference]:
        """
        提取代码文件中的外部引用
        """
        if not self.parser:
            return []
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                source_code = f.read()
            
            tree = self.parser.parse(source_code.encode('utf-8'))
            references = []
            
            # 遍历AST提取外部引用
            self._traverse_tree(tree.root_node, source_code, file_path, references)
            
            return references
            
        except Exception as e:
            logger.error(f"解析文件失败 {file_path}: {str(e)}")
            return []
    
    def _traverse_tree(self, node, source_code: str, file_path: str, references: List[ExternalReference]):
        """遍历AST节点"""
        # 检查函数调用
        if node.type == 'call_expression':
            function_node = node.child_by_field_name('function')
            if function_node and function_node.type == 'identifier':
                func_name = source_code[function_node.start_byte:function_node.end_byte]
                references.append(ExternalReference(
                    name=func_name,
                    type='function',
                    file_path=file_path,
                    line_number=node.start_point[0] + 1,
                    context=source_code[node.start_byte:node.end_byte]
                ))
        
        # 检查变量引用
        elif node.type == 'identifier':
            var_name = source_code[node.start_byte:node.end_byte]
            # 简单过滤，避免关键字
            if not var_name in ['if', 'for', 'while', 'return', 'int', 'char', 'void']:
                references.append(ExternalReference(
                    name=var_name,
                    type='variable',
                    file_path=file_path,
                    line_number=node.start_point[0] + 1,
                    context=var_name
                ))
        
        # 递归遍历子节点
        for child in node.children:
            self._traverse_tree(child, source_code, file_path, references)

class Neo4jManager:
    """Neo4j图数据库管理器"""
    
    def __init__(self, uri: str = "bolt://localhost:7687", 
                 username: str = "neo4j", password: str = "password"):
        self.driver = GraphDatabase.driver(uri, auth=(username, password))
        
    def __enter__(self):
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()
        
    def close(self):

        if self.driver:
            self.driver.close()
    
    def clear_database(self):

        with self.driver.session() as session:
            session.run("MATCH (n) DETACH DELETE n")
            logger.info("数据库已清空")
    
    def import_graph_data(self, nodes: List[GraphNode], edges: List[GraphEdge]):
        """
        导入图数据到Neo4j
        
        """
        with self.driver.session() as session:
            # 批量导入节点
          
            for i in range(0, len(nodes), 1000):  # 分批处理
                batch_nodes = nodes[i:i+1000]
                self._import_nodes_batch(session, batch_nodes)
            
            # 批量导入边
 
            for i in range(0, len(edges), 1000):
                batch_edges = edges[i:i+1000]
                self._import_edges_batch(session, batch_edges)
            
  
    
    def _import_nodes_batch(self, session, nodes: List[GraphNode]):

        query = """
        UNWIND $nodes AS node
        CREATE (n)
        SET n = node.properties
        SET n:node.label
        SET n.id = node.id
        """
        
        node_data = [
            {
                "id": node.id,
                "label": node.label,
                "properties": node.properties
            }
            for node in nodes
        ]
        
        session.run(query, nodes=node_data)
    
    def _import_edges_batch(self, session, edges: List[GraphEdge]):

        query = """
        UNWIND $edges AS edge
        MATCH (source {id: edge.source})
        MATCH (target {id: edge.target})
        CALL apoc.create.relationship(source, edge.relationship, edge.properties, target)
        YIELD rel
        RETURN count(rel)
        """
        
        edge_data = [
            {
                "source": edge.source,
                "target": edge.target,
                "relationship": edge.relationship,
                "properties": edge.properties
            }
            for edge in edges
        ]
        
        try:
            session.run(query, edges=edge_data)
        except Exception as e:
            # 如果没有APOC插件，使用基础Cypher
            logger.warning("APOC不可用，使用基础Cypher导入边")
            self._import_edges_basic(session, edges)
    
    def _import_edges_basic(self, session, edges: List[GraphEdge]):
      
        for edge in edges:
            query = f"""
            MATCH (source {{id: $source}})
            MATCH (target {{id: $target}})
            CREATE (source)-[r:{edge.relationship}]->(target)
            SET r = $properties
            """
            
            session.run(query, 
                       source=edge.source, 
                       target=edge.target, 
                       properties=edge.properties)

class DependencyQueryService:
   
    
    def __init__(self, neo4j_manager: Neo4jManager):
        self.neo4j = neo4j_manager
    
    def query_variable_definition(self, variable_name: str, file_path: str = None) -> List[Dict]:
    
        query = """
        MATCH (n:IDENTIFIER {name: $var_name})
        WHERE n.filename CONTAINS $file_path OR $file_path IS NULL
        RETURN n.name, n.filename, n.lineNumber, n.columnNumber
        ORDER BY n.lineNumber
        """
        
        with self.neo4j.driver.session() as session:
            result = session.run(query, var_name=variable_name, 
                               file_path=file_path or "")
            return [record.data() for record in result]
    
    def query_function_calls(self, file_path: str) -> List[Dict]:
       
        query = """
        MATCH (m:METHOD)-[:CONTAINS]->(c:CALL)
        WHERE m.filename = $file_path
        RETURN c.name as function_name, c.lineNumber, m.name as caller_method
        ORDER BY c.lineNumber
        """
        
        with self.neo4j.driver.session() as session:
            result = session.run(query, file_path=file_path)
            return [record.data() for record in result]
    
    def query_type_definitions(self, type_name: str) -> List[Dict]:
       
        query = """
        MATCH (t:TYPE {name: $type_name})
        RETURN t.name, t.fullName, t.filename
        """
        
        with self.neo4j.driver.session() as session:
            result = session.run(query, type_name=type_name)
            return [record.data() for record in result]
    
    def query_file_includes(self, file_path: str) -> List[Dict]:
       
        query = """
        MATCH (f:FILE {name: $file_path})-[:INCLUDES]->(included:FILE)
        RETURN included.name as included_file
        """
        
        with self.neo4j.driver.session() as session:
            result = session.run(query, file_path=file_path)
            return [record.data() for record in result]

class CodeRepositoryGraphGenerator:

    
    def __init__(self, joern_path: str = "/opt/joern", 
                 neo4j_uri: str = "bolt://localhost:7687",
                 neo4j_user: str = "neo4j", neo4j_password: str = "password"):
        
        self.joern_analyzer = JoernAnalyzer(joern_path)
        self.tree_sitter_parser = TreeSitterParser()
        self.neo4j_manager = Neo4jManager(neo4j_uri, neo4j_user, neo4j_password)
        self.query_service = DependencyQueryService(self.neo4j_manager)
    
    def process_repository(self, repo_path: str, clear_db: bool = True) -> bool:

        try:
            logger.info("开始处理代码仓库...")
            
            # 清空数据库（可选）
            if clear_db:
                self.neo4j_manager.clear_database()
            
            # 1. 使用Joern分析代码
            cpg_path = self.joern_analyzer.analyze_repository(repo_path)
            
            # 2. 提取图数据
            nodes, edges = self.joern_analyzer.extract_graph_data(cpg_path)
            
            # 3. 导入Neo4j数据库
            self.neo4j_manager.import_graph_data(nodes, edges)
            
          
            return True
            
        except Exception as e:
            logger.error(f"处理失败: {str(e)}")
            return False
    
    def analyze_file_dependencies(self, file_path: str) -> Dict[str, Any]:

        result = {
            "file_path": file_path,
            "external_references": [],
            "variable_definitions": [],
            "function_calls": [],
            "type_definitions": [],
            "file_includes": []
        }
        
        try:
            # 1. 使用Tree-Sitter提取外部引用
            external_refs = self.tree_sitter_parser.extract_external_references(file_path)
            result["external_references"] = [
                {
                    "name": ref.name,
                    "type": ref.type,
                    "line": ref.line_number,
                    "context": ref.context
                }
                for ref in external_refs
            ]
            
            # 2. 查询变量定义
            for ref in external_refs:
                if ref.type == "variable":
                    var_defs = self.query_service.query_variable_definition(
                        ref.name, file_path
                    )
                    result["variable_definitions"].extend(var_defs)
            
            # 3. 查询函数调用
            func_calls = self.query_service.query_function_calls(file_path)
            result["function_calls"] = func_calls
            
            # 4. 查询类型定义
            for ref in external_refs:
                if ref.type == "type":
                    type_defs = self.query_service.query_type_definitions(ref.name)
                    result["type_definitions"].extend(type_defs)
            
            # 5. 查询文件包含关系
            includes = self.query_service.query_file_includes(file_path)
            result["file_includes"] = includes
            
        except Exception as e:
            result["error"] = str(e)
        
        return result
    
    def close(self):
      
        self.neo4j_manager.close()


def main():
 
    generator = CodeRepositoryGraphGenerator(
        joern_path="/opt/joern",
        neo4j_uri="bolt://localhost:7687",
        neo4j_user="neo4j",
        neo4j_password="password"
    )
    
    try:
        # 处理代码仓库
        repo_path = "/path/to/c/repository"
        success = generator.process_repository(repo_path)
        
        if success:
            file_path = "/path/to/c/repository/src/main.c"
            dependencies = generator.analyze_file_dependencies(file_path)
            
            
    finally:
        generator.close()

if __name__ == "__main__":
    main()