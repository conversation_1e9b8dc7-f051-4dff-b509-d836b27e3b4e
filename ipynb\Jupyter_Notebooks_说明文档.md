# Jupyter Notebooks 说明文档



## 文件列表与功能

### 1. envi.ipynb - 环境配置与依赖安装

**主要功能**: 系统环境初始化和依赖库安装

**核心内容**:
- **依赖安装**: 
  ```bash
  pip install transformers
  pip install faiss-cpu
  ```
- **网络代理配置**: 设置代理环境变量以访问外部资源
- **CodeBERT模型下载**: 从Hugging Face下载预训练模型

**关键代码段**:
```python
# 模型下载函数
def download_codebert():
    tokenizer = RobertaTokenizer.from_pretrained(
        "microsoft/codebert-base", 
        cache_dir="./codebert_model"
    )
    model = RobertaModel.from_pretrained(
        "microsoft/codebert-base", 
        cache_dir="./codebert_model"
    )
    return tokenizer, model
```

**使用场景**: 首次部署系统时的环境准备

---

### 2. preprocess.ipynb - 数据预处理

**主要功能**: Juliet测试套件数据的预处理和标准化

**核心内容**:
- **ZIP文件解压**: 自动解压Juliet测试套件
- **代码块提取**: 分离BAD和GOOD代码片段
- **代码清理**: 移除注释、多余空白和测试框架代码
- **CWE标签提取**: 从文件名提取漏洞类型标识

**关键功能**:
```python
def extract_code(content, start_tag, end_tag):
    """提取#ifndef OMITBAD和#ifndef OMITGOOD之间的代码块"""
    
def anonymize_cwe_functions(code_block):
    """将CWE开头的函数名替换为通用占位符"""
    
def analyze_file(file_path):
    """分析单个C文件，提取漏洞和安全代码片段"""
```

**输出格式**:
```json
{
  "file_name": "CWE789_example.c",
  "CWE_ID": "CWE-789", 
  "IF_VUL": true/false,
  "Description": "漏洞描述",
  "code": "处理后的代码片段"
}
```

**处理统计**: 处理约19万条代码记录

---

### 3. codebert.ipynb - CodeBERT嵌入生成

**主要功能**: 使用CodeBERT模型为代码片段生成向量嵌入

**核心内容**:
- **模型加载**: 加载本地CodeBERT模型
- **批量处理**: 对大量代码片段进行向量化
- **进度监控**: 使用tqdm显示处理进度
- **嵌入存储**: 将768维向量保存到JSON文件

**关键实现**:
```python
def add_codebert_embeddings_to_json(json_file_path, output_json_file_path):
    # 加载预处理的代码数据
    with open(json_file_path, 'r') as file:
        data = json.load(file)
    
    # 批量生成嵌入
    for item in tqdm(data):
        inputs = tokenizer(item["code"], return_tensors="pt", 
                          truncation=True, max_length=512)
        with torch.no_grad():
            outputs = model(**inputs)
            embedding = outputs.last_hidden_state[:, 0, :].squeeze()
            item["codebert_embedding"] = embedding.tolist()
```

**性能指标**:
- 处理速度: ~28条/秒
- 总处理时间: 约1小时54分钟
- 向量维度: 768维

---

### 4. graphcodebert-base.ipynb - GraphCodeBERT嵌入生成

**主要功能**: 使用GraphCodeBERT模型生成增强的代码嵌入

**核心内容**:
- **模型差异**: 相比CodeBERT，GraphCodeBERT考虑了代码的图结构信息
- **嵌入生成**: 为相同的代码数据集生成GraphCodeBERT嵌入
- **性能对比**: 与CodeBERT嵌入进行对比分析

**关键特性**:
```python
# GraphCodeBERT模型加载
MODEL_NAME = "../graphcodebert-base"
tokenizer = RobertaTokenizer.from_pretrained(MODEL_NAME)
model = RobertaModel.from_pretrained(MODEL_NAME)

def add_graphcodebert_embeddings_to_json():
    # 生成GraphCodeBERT嵌入
    item["graphcodebert_embedding"] = embedding.tolist()
```

**模型警告**: 
- 需要在下游任务上进行微调以获得最佳性能
- 某些权重未从预训练模型初始化

---

### 5. VectorBD2.ipynb - FAISS向量索引构建

**主要功能**: 构建基于余弦相似度的FAISS向量检索索引

**核心内容**:
- **向量归一化**: 对嵌入向量进行L2归一化以支持余弦相似度
- **索引构建**: 使用FAISS IndexFlatIP构建高效检索索引
- **元数据管理**: 同步保存向量索引和对应的元数据
- **批量处理**: 分批处理大规模向量数据

**关键算法**:
```python
def normalize(vectors):
    """L2归一化向量以支持余弦相似度计算"""
    norms = np.linalg.norm(vectors, axis=1, keepdims=True)
    return vectors / norms

def build_faiss_index_cosine_similarity():
    # 创建IndexFlatIP索引（内积索引，用于余弦相似度）
    index = faiss.IndexFlatIP(dimension)
    
    # 批量添加归一化向量
    normalized_batch = normalize(batch_embeddings)
    index.add(normalized_batch)
```

**索引规格**:
- 索引类型: FlatIP (内积索引)
- 向量维度: 768
- 数据规模: 19万+向量
- 批处理大小: 1000条/批

---

### 6. query2.ipynb - 查询测试与验证

**主要功能**: 端到端的漏洞检测流程测试和验证

**核心内容**:
- **代码预处理测试**: 验证代码清理和匿名化功能
- **向量检索测试**: 测试FAISS索引的检索性能
- **静态分析集成**: 集成PVS-Studio进行静态代码分析
- **完整流程验证**: 模拟真实的漏洞检测场景

**测试用例1**: 逻辑错误检测
```c
int main(int argc, const char* argv[]) {
    if (argc != argc) {  // 明显的逻辑错误
        return 1;
    }
}
```

**测试用例2**: 缓冲区溢出检测
```c
void fun() {
    FILE * f;
    char buf[10];
    f = fopen("TestInputFile1", "r");
    fgets(buf, 11, f);  // 缓冲区溢出风险
    fclose(f);
}
```

**检索结果示例**:
```
Rank 1: CWE-675 (Duplicate Operations on Resource)
距离: 10.48
相似代码: FILE操作相关的漏洞样本
```

**静态分析结果**:
```xml
<PVS-Studio_Analysis_Log>
    <ErrorCode>V501</ErrorCode>
    <Message>There are identical sub-expressions: argc != argc</Message>
    <CWECode>CWE-570</CWECode>
</PVS-Studio_Analysis_Log>
```

## 工作流程图

```mermaid
graph TD
    A[envi.ipynb] --> B[环境配置完成]
    B --> C[preprocess.ipynb]
    C --> D[数据预处理完成]
    D --> E[codebert.ipynb]
    D --> F[graphcodebert-base.ipynb]
    E --> G[CodeBERT嵌入]
    F --> H[GraphCodeBERT嵌入]
    G --> I[VectorBD2.ipynb]
    H --> I
    I --> J[FAISS索引构建]
    J --> K[query2.ipynb]
    K --> L[端到端测试验证]
```

## 数据流转

1. **原始数据**: Juliet测试套件ZIP文件
2. **预处理数据**: analyzed_files.json (19万+记录)
3. **嵌入数据**: 
   - analyzed_files_with_embeddings.json (CodeBERT)
   - analyzed_files_with_graphcodebert_embeddings.json (GraphCodeBERT)
4. **索引文件**: 
   - cosine_similarity_index.faiss (向量索引)
   - cosine_similarity_index.faiss.metadata (元数据)
5. **查询结果**: top_results.json (检索结果)

## 性能指标

### 处理能力
- **数据规模**: 192,146条代码记录
- **处理速度**: 28条/秒 (嵌入生成)
- **索引构建**: 193批次，约1小时51分钟
- **内存使用**: 峰值约8-16GB

### 检索性能
- **检索延迟**: <100ms (单次查询)
- **索引大小**: 约500MB-1GB
- **支持并发**: 多线程查询支持

## 使用建议

### 1. 执行顺序
按照编号顺序执行notebook：
1. envi.ipynb (环境配置)
2. preprocess.ipynb (数据预处理)  
3. codebert.ipynb 或 graphcodebert-base.ipynb (嵌入生成)
4. VectorBD2.ipynb (索引构建)
5. query2.ipynb (测试验证)

### 2. 资源要求
- **内存**: 建议16GB以上
- **存储**: 至少100GB可用空间
- **GPU**: 可选，用于加速模型推理

### 3. 注意事项
- 确保网络连接稳定（模型下载）
- 定期保存中间结果
- 监控内存使用情况
- 备份重要的索引文件

## 故障排除

### 常见问题
1. **模型下载失败**: 检查网络代理配置
2. **内存不足**: 减少批处理大小
3. **索引损坏**: 重新运行VectorBD2.ipynb
4. **依赖冲突**: 使用虚拟环境隔离

### 调试技巧
- 启用详细日志输出
- 使用小数据集进行测试
- 分步骤验证每个环节
- 保存中间状态便于恢复

## 扩展开发

### 1. 新模型集成
- 添加其他预训练代码模型
- 实现模型性能对比
- 支持多模型融合

### 2. 索引优化
- 尝试不同的FAISS索引类型
- 实现增量索引更新
- 优化检索参数

### 3. 评估指标
- 添加检索准确率评估
- 实现A/B测试框架
- 构建基准测试集
