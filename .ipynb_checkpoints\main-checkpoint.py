from utils import perform_query  # Import the refactored function
from utils import clean_code_block,anonymize_cwe_functions,preprocess_code
from SAST import write_to_main_cpp,run_cmake_and_analyze,parse_pvs_analysis_report
from promptGen import generate_prompt,read_json_file

import requests

def main():
    query_code = r"""

void vulnerable_function(char *input) {
    char buffer[50];
    int length = strlen(input);
    
    if (length > 50) {
        printf("Input is too large.\n");
        return;
    }
    
    memcpy(buffer, input, length);
    buffer[length] = '\0';
    printf("Buffer content: %s\n", buffer);
}

    """
    query_code = preprocess_code(query_code)
    # Perform the query and print the results
    results = perform_query(query_code)
    
    write_to_main_cpp(query_code,output_dir="/root/preprocess/SAST")
    project_dir = "/root/preprocess/SAST/build"  

    # 生成报告
    report_path = run_cmake_and_analyze(project_dir)
    
    # 输出报告路径
    if report_path:
        print(f"Analysis report generated at: {report_path}")
    else:
        print("Analysis report generation failed.")


    report_path = "/root/preprocess/SAST/build/pvs_analysis.xml"  

    # 解析并输出分析报告内容
    SAST_Log = parse_pvs_analysis_report(report_path)


        
    file_path = '/root/preprocess/top_results.json'  #
    json_data = read_json_file(file_path)

    # 打印读取的JSON内容
    if json_data is not None:
        print("JSON Data Loaded:")
        print(json_data)
    else:
        print("Failed to load JSON data.")

    prompt = generate_prompt(SAST_Log, json_data,query_code)

    print(prompt)


    url = "https://api.siliconflow.cn/v1/chat/completions"

    payload = {
        "model": "Qwen/Qwen2.5-Coder-7B-Instruct",
        "stream": False,
        "max_tokens": 4096,
        "temperature": 0.7,
        "top_p": 0.7,
        "top_k": 50,
        "frequency_penalty": 0.5,
        "n": 1,
        "messages": [
            {
                "content": prompt,
                "role": "user"
            }
        ]
    }
    headers = {
        "Authorization": "Bearer sk-eapbrmwvyxhesrlonzxetsqvlhqsrlepmwtkeuzomhktpvcl",
        "Content-Type": "application/json"
    }

    response = requests.request("POST", url, json=payload, headers=headers)

    print(response.text)


    
if __name__ == "__main__":
    main()
