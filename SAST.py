import os

def write_to_main_cpp(preprocessed_code, output_dir="/root/preprocess/SAST"):
    """
    Args:
        preprocessed_code (str): 预处理的代码。
        output_dir (str): PVS-Studio 项目所在的目录
    """
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # 创建并写入 main.cpp 文件
    main_cpp_path = os.path.join(output_dir, "main.cpp")
    with open(main_cpp_path, 'w') as f:
        # 添加 PVS-Studio 识别的开头注释
        f.write("// This is an open source non-commercial project. Dear PVS-Studio, please check it.\n")
        f.write("// PVS-Studio Static Code Analyzer for C, C++ and C#: http://www.viva64.com\n\n") 
        import os

def write_to_main_cpp(preprocessed_code, output_dir="/root/preprocess/SAST"):
    """
    Args:
        preprocessed_code (str): 预处理的代码。
        output_dir (str): PVS-Studio 项目所在的目录
    """
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # 创建并写入 main.cpp 文件
    main_cpp_path = os.path.join(output_dir, "main.cpp")
    with open(main_cpp_path, 'w') as f:
        # 添加 PVS-Studio 识别的开头注释
        f.write("// This is an open source non-commercial project. Dear PVS-Studio, please check it.\n")
        f.write("// PVS-Studio Static Code Analyzer for C, C++ and C#: http://www.viva64.com\n\n") 
        
        # Add common header files
        common_headers = [
            "#include <iostream>",  # Standard I/O stream
            "#include <vector>",  # Vector container
            "#include <string>",  # String handling
            "#include <algorithm>",  # Standard algorithms
            "#include <memory>",  # Smart pointers
            "#include <map>",  # Map container
            "#include <set>",  # Set container
            "#include <stdexcept>",  # Standard exceptions
            "#include <cstdio>"  # C standard input/output
        ]
        
        # Write headers to the file
        for header in common_headers:
            f.write(header + "\n")

        # Add a newline before the preprocessed code starts
        f.write("\n")
        f.write(preprocessed_code)

    print(f"代码已写入 {main_cpp_path}")



import subprocess
import xml.etree.ElementTree as ET

def run_cmake_and_analyze(project_dir):
    """
    执行 CMake 构建和 PVS-Studio 静态分析，并读取生成的 XML 报告。
    
    Args:
        project_dir (str): PVS-Studio 项目的目录（包含 CMakeLists.txt 和源代码）。
    -
    Returns:
        str: 分析报告的路径。
    """
    try:
        # 获取绝对路径并检查目录是否存在
        # abs_project_dir = os.path.abspath(project_dir)
        # print(f"Checking directory: {abs_project_dir}")
        
        # if not os.path.exists(abs_project_dir):
        #     raise FileNotFoundError(f"The directory {abs_project_dir} does not exist.")

        # 进入项目目录
        # os.chdir(abs_project_dir)

        # # 执行 CMake 配置
        # print("Running CMake...")
        # subprocess.check_call(['cmake', '.'])

        # # 执行构建过程并进行静态分析
        # print("Building and running PVS-Studio analysis...")
        # subprocess.check_call(['cmake', '--build', '.', '--target', 'example1.analyze'])
        
        # 执行静态分析
        print("Running PVS-Studio analysis...")
        subprocess.check_call(['pvs-studio-analyzer', 'analyze', '-f','/root/preprocess/SAST/compile_commands.json',
                               '-o', 'pvs.log', '-e', 'excludepath'])

        # 转换分析报告
        print("Converting PVS-Studio log to report...")
        subprocess.check_call(['plog-converter', '-a', 'GA:1,2', '-t', 'xml', '-o', '/root/preprocess/SAST/pvs_analysis.xml', 'pvs.log'])
        
        # 获取报告文件的路径
        report_path = "/root/preprocess/SAST/pvs_analysis.xml"
        
        if not os.path.exists(report_path):
            raise FileNotFoundError(f"PVS-Studio analysis report not found at {report_path}")

        return report_path

    except subprocess.CalledProcessError as e:
        print(f"Error during CMake build or analysis: {e}")
        return None



project_dir = "/root/preprocess/SAST/build"  # 项目目录

# 运行 CMake 并生成报告
report_path = run_cmake_and_analyze(project_dir)

# 输出报告路径
if report_path:
    print(f"Analysis report generated at: {report_path}")
else:
    print("Analysis report generation failed.")


import xml.etree.ElementTree as ET
def parse_pvs_analysis_report(report_path):
    """
    解析 PVS-Studio 静态分析报告，并输出相关信息。

    """
    report_str = ""  # 用于存储分析报告的字符串

    try:
        # 解析 XML 文件
        tree = ET.parse(report_path)
        root = tree.getroot()

        # 遍历所有 <PVS-Studio_Analysis_Log> 元素
        for log_entry in root.findall('.//PVS-Studio_Analysis_Log'):
            # 提取每个问题的信息
            error_type = log_entry.find('ErrorType').text if log_entry.find('ErrorType') is not None else 'N/A'
            error_code = log_entry.find('ErrorCode').text if log_entry.find('ErrorCode') is not None else 'N/A'
            message = log_entry.find('Message').text if log_entry.find('Message') is not None else 'N/A'
            line = log_entry.find('Line').text if log_entry.find('Line') is not None else 'N/A'
            file_name = log_entry.find('File').text if log_entry.find('File') is not None else 'N/A'
            cwe_code = log_entry.find('CWECode').text if log_entry.find('CWECode') is not None else 'N/A'

            # 拼接每个问题的详细信息到字符串
            report_str += f"Error Type: {error_type}\n"
            report_str += f"Error Code: {error_code}\n"
            report_str += f"Message: {message}\n"
            report_str += f"File: {file_name}\n"
            report_str += f"Line: {line}\n"
            report_str += f"CWE Code: {cwe_code}\n"
            report_str += "-" * 50 + "\n"

    except Exception as e:
        report_str = f"Error parsing the PVS-Studio report: {e}"

    return report_str



