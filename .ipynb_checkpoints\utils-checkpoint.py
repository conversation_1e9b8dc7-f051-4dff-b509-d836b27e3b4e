import re

def clean_code_block(code_block):

    if not isinstance(code_block, str) or not code_block.strip():
        return ""

    try:
        # Remove single-line comments (// ...)
        code_block = re.sub(r"//.*", "", code_block)
        # Remove multi-line comments (/* ... */)
        code_block = re.sub(r"/\*[\s\S]*?\*/", "", code_block)
        
        # Strip extra whitespace lines
        code_block = "\n".join([line.strip() for line in code_block.splitlines() if line.strip()])
        return code_block
    except Exception as e:
        raise ValueError(f"Error cleaning code block: {e}")

def anonymize_cwe_functions(code_block):
    """
    Anonymize all functions or identifiers with 'CWE' in their name.
    """
    if not isinstance(code_block, str) or not code_block.strip():
        return ""

    try:
        placeholder_mapping = {}  # Map to store function name and its placeholder
        function_pattern = r"\b(CWE\w+)\b"  # Match functions or identifiers starting with CWE

        def replace_function_name(match):
            """
            Replace matched CWE function with a consistent placeholder.
            """
            function_name = match.group(1)
            if function_name not in placeholder_mapping:
                placeholder_mapping[function_name] = f"func_{len(placeholder_mapping) + 1}"
            return placeholder_mapping[function_name]

        # Replace all CWE-prefixed functions with placeholders
        anonymized_code = re.sub(function_pattern, replace_function_name, code_block)
        return anonymized_code
    except Exception as e:
        raise ValueError(f"Error anonymizing CWE functions: {e}")

def preprocess_code(code_block):
    """
    Preprocess the code block by cleaning it and anonymizing CWE functions.

    Args:
        code_block (str): The input code block.

    Returns:
        str: The preprocessed code block.
    """
    # Step 1: Clean the code block
    cleaned_code = clean_code_block(code_block)
    # Step 2: Anonymize CWE-prefixed functions
    anonymized_code = anonymize_cwe_functions(cleaned_code)
    return anonymized_code
    



import faiss
import numpy as np
import json
def search_faiss_index_flat(query_embedding, index_file_path, top_k=5):
    """
    使用 FlatL2 索引对查询嵌入进行检索
    :param query_embedding: 输入的查询嵌入 (1D NumPy 数组)
    :param index_file_path: FAISS 索引文件路径
    :param top_k: 返回的最近邻条目的数量
    :return: 检索到的元数据结果
    """
    # 加载索引
    index = faiss.read_index(index_file_path)

    # 加载元数据
    with open(index_file_path + ".metadata", 'r', encoding='utf-8') as file:
        metadata = json.load(file)

    # 将查询嵌入转换为 NumPy 格式
    query_np = np.array([query_embedding], dtype=np.float32)

    # 检索最近邻
    distances, indices = index.search(query_np, top_k)

    # 映射查询结果到元数据
    results = []
    for i in range(top_k):
        idx = indices[0][i]
        if idx < len(metadata):
            result = metadata[idx]
            result["distance"] = distances[0][i]
            results.append(result)

    return results


from transformers import RobertaTokenizer, RobertaModel
import torch
def perform_query(query_code):
    # 加载 CodeBERT 模型和 Tokenizer
    LOCAL_MODEL_DIR = "/root/codebert-base"
    tokenizer = RobertaTokenizer.from_pretrained(LOCAL_MODEL_DIR)
    model = RobertaModel.from_pretrained(LOCAL_MODEL_DIR)

    # 输入查询代码（用户可能输入的文本）
    query_code = query_code

    # 将代码转化为嵌入
    inputs = tokenizer(query_code, return_tensors="pt", truncation=True, max_length=512)
    with torch.no_grad():
        outputs = model(**inputs)
        query_embedding = outputs.last_hidden_state[:, 0, :].squeeze().numpy()

    # 使用索引检索
    results = search_faiss_index_flat(query_embedding, "/root/preprocess/large_embedding_index.faiss", top_k=5)

    # 打印检索结果
    for i, result in enumerate(results):
        print(f"Rank {i + 1}:")
        print(f"  文件名: {result['file_name']}")
        print(f"  CWE_ID: {result['CWE_ID']}")
        print(f"  是否为漏洞: {result['IF_VUL']}")
        print(f"  距离: {result['distance']}")
        print(f"  代码片段: {result['code']}\n")
    for result in results:
        result['distance'] = float(result['distance'])
    top_results = results[:2]

    # 存储到 JSON 文件
    output_file = '/root/preprocess/top_results.json'

    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(top_results, f, indent=4, ensure_ascii=False)
        print(f"Top 3 results have been saved to {output_file}")
    except Exception as e:
        print(f"Error while writing JSON file: {e}")